export const getConfig = (env: string) => {
  const validEnvs = ["int-cac1", "uat-cac1", "preprod-cac1", "prod-cac1"];
  if (!validEnvs.includes(env)) {
    throw new Error(
      `Invalid environment: ${env}. Must be one of ${validEnvs.join(", ")}`
    );
  }

  try {
    return require(`../../../../configs/${env}.json`);
  } catch (error) {
    throw new Error(`Configuration file for environment ${env}.json not found`);
  }
};