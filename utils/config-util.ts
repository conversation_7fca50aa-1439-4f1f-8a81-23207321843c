import { AppConfig, ValidEnvironment, isValidEnvironment, isAppConfig } from "../configs/config.interface";

/**
 * Loads and validates configuration for the specified environment
 * @param env - Environment name
 * @returns Typed configuration object
 * @throws Error if environment is invalid or config file is not found/invalid
 */
export const getConfig = (env: string): AppConfig => {
  if (!isValidEnvironment(env)) {
    throw new Error(
      `Invalid environment: ${env}. Must be one of: int-cac1, bat-cac1, crt-cac1, preprod-cac1, prod-cac1`
    );
  }

  try {
    const config = require(`../configs/${env}.json`);

    if (!isAppConfig(config)) {
      throw new Error(`Invalid configuration structure in ${env}.json`);
    }

    return config;
  } catch (error) {
    if (error instanceof Error && error.message.includes('Invalid configuration structure')) {
      throw error;
    }
    throw new Error(`Configuration file for environment ${env}.json not found`);
  }
};

/**
 * Gets the list of valid environments
 * @returns Array of valid environment names
 */
export const getValidEnvironments = (): ValidEnvironment[] => {
  return ["int-cac1", "bat-cac1", "crt-cac1", "preprod-cac1", "prod-cac1"];
};