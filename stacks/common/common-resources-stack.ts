import * as cdk from "aws-cdk-lib";
import * as ec2 from 'aws-cdk-lib/aws-ec2';
import { Construct } from "constructs";

export class CommonResourceStack extends cdk.Stack {

  constructor(scope: Construct, id: string, props: any) {
    super(scope, id, {
      env: {
        account: props.config.account,
        region: props.config.region,
      },
      stackName: props.stackConfigProps.stackName,
      ...props,
      synthesizer: props.synthesizer
    });

    // Instantiate resources
    const vpc = ec2.Vpc.fromLookup(this, 'VPC', {
      vpcId: props.config.vpc.vpcId,
      isDefault: false
    });
    
    const securityGroupId = cdk.Fn.importValue('sgIdDigitalOdsRdsCredentials')
    const securityGroups = ec2.SecurityGroup.fromSecurityGroupId(this, 'sgIdDigitalOdsRdsCredentials', securityGroupId);

  }
}