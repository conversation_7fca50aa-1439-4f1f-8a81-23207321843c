# Configuration Interface

This folder contains the TypeScript interface definitions for the application configuration files.

## Files

- `config.interface.ts` - TypeScript interface definitions for all configuration structures
- `*.json` - Environment-specific configuration files

## Usage

### Importing the Interface

```typescript
import { AppConfig, ValidEnvironment } from '../configs/config.interface';
```

### Using with config-util

The `config-util.ts` has been updated to use the interface and provides type safety:

```typescript
import { getConfig } from '../utils/config-util';

// This now returns a fully typed AppConfig object
const config = getConfig('int-cac1');

// TypeScript will provide intellisense and type checking
console.log(config.account); // string
console.log(config.vpc.vpcId); // string
console.log(config.lambda.timeout); // number
```

### Configuration Structure

The main `AppConfig` interface defines the complete structure of environment configuration files:

```typescript
interface AppConfig {
  account: string;
  region: string;
  environment: string;
  stackName: string;
  tags: ResourceTags;
  vpc: VpcConfig;
  secretsManager: SecretsManagerConfig;
  lambda: LambdaConfig;
  defaultStackSynthesizer: DefaultStackSynthesizerConfig;
  stacks: StacksConfig;
}
```

### Type Safety Features

1. **Type Guards**: The interface includes type guard functions to validate configuration at runtime
2. **Environment Validation**: `ValidEnvironment` type ensures only valid environment names are used
3. **Nested Interfaces**: All nested objects have their own interfaces for better type safety

### Example Configuration File Structure

```json
{
  "account": "************",
  "region": "ca-central-1",
  "environment": "int-cac1",
  "stackName": "ac-odh-batch-distribution-webfocus",
  "tags": {
    "tower": "operations",
    "department-id": "1904",
    // ... other tags
  },
  "vpc": {
    "vpcId": "vpc-04cb4138a48c3cbaf",
    "securityGroupId": "sg-07612cb342a5be91d",
    "subnets": ["subnet-1", "subnet-2"]
  },
  "secretsManager": {
    "traxDbSecret": "intca1/trax-external-db/db-credentials",
    "dynatraceCredentialsSecretName": "/int-cac1/dbaas/dynatrace/oneagent_credentials"
  },
  "lambda": {
    "timeout": 300,
    "memorySize": 1024,
    "logging": {
      "retentionDays": 14
    }
  },
  "defaultStackSynthesizer": {
    "cdkBootstrapConfigSSM": "/odh/int-cac1/cdk-bootstrap/config",
    "bucketPrefix": "ODH-PNR-STORE/ac-odh-shared-resources/int-cac1/"
  },
  "stacks": {
    "CommonResourceStack": {
      "stackName": "ac-odh-webfocus-common-resources",
      "resources": {
        "s3UploaderLambda": {
          "functionName": "ac-odh-webfocus-common-resources-uploader"
        }
      }
    }
  }
}
```

### Benefits

1. **IntelliSense**: Full autocomplete support in IDEs
2. **Type Checking**: Compile-time validation of configuration usage
3. **Documentation**: Self-documenting code with interface definitions
4. **Refactoring Safety**: Changes to configuration structure are caught at compile time
5. **Runtime Validation**: Type guards ensure configuration files match expected structure

### Adding New Configuration Properties

When adding new properties to configuration files:

1. Update the appropriate interface in `config.interface.ts`
2. Update the type guard function if necessary
3. Update this README with examples
4. Ensure all environment configuration files include the new properties
