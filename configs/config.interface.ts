/**
 * Configuration interface for the AC ODH Batch Distribution WebFocus application
 * This interface defines the structure of environment-specific configuration files
 */

/**
 * AWS VPC configuration
 */
export interface VpcConfig {
  /** VPC ID */
  vpcId: string;
  /** Security Group ID */
  securityGroupId: string;
  /** Array of subnet IDs */
  subnets: string[];
}

/**
 * AWS Secrets Manager configuration
 */
export interface SecretsManagerConfig {
  /** Secret name for TRAX database credentials */
  traxDbSecret: string;
  /** Secret name for Dynatrace OneAgent credentials */
  dynatraceCredentialsSecretName: string;
}

/**
 * Lambda logging configuration
 */
export interface LambdaLoggingConfig {
  /** CloudWatch log retention period in days */
  retentionDays: number;
}

/**
 * Lambda function configuration
 */
export interface LambdaConfig {
  /** Function timeout in seconds */
  timeout: number;
  /** Memory allocation in MB */
  memorySize: number;
  /** Logging configuration */
  logging: LambdaLoggingConfig;
}

/**
 * CDK Default Stack Synthesizer configuration
 */
export interface DefaultStackSynthesizerConfig {
  /** SSM parameter path for CDK bootstrap configuration */
  cdkBootstrapConfigSSM: string;
  /** S3 bucket prefix for CDK assets */
  bucketPrefix: string;
}

/**
 * S3 Uploader Lambda resource configuration
 */
export interface S3UploaderLambdaConfig {
  /** Lambda function name */
  functionName: string;
}

/**
 * Common Resource Stack resources configuration
 */
export interface CommonResourceStackResources {
  /** S3 uploader lambda configuration */
  s3UploaderLambda: S3UploaderLambdaConfig;
}

/**
 * Common Resource Stack configuration
 */
export interface CommonResourceStackConfig {
  /** Stack name */
  stackName: string;
  /** Stack resources configuration */
  resources: CommonResourceStackResources;
}

/**
 * Stacks configuration
 */
export interface StacksConfig {
  /** Common Resource Stack configuration */
  CommonResourceStack: CommonResourceStackConfig;
}

/**
 * AWS resource tags
 * Extends Record<string, string> to be compatible with CDK tagging utilities
 */
export interface ResourceTags extends Record<string, string> {
  /** Tower classification */
  tower: string;
  /** Department ID */
  "department-id": string;
  /** Department name */
  "department-name": string;
  /** Cost code for billing */
  CostCode: string;
  /** Project name */
  ProjectName: string;
  /** Whether this is a shared resource */
  SharedResource: string;
  /** Application name */
  Application: string;
  /** Technical owner */
  TechOwner: string;
  /** Business owner */
  BusinessOwner: string;
  /** System criticality level */
  Criticality: string;
  /** Data sensitivity level */
  Sensitivity: string;
  /** Recovery Time Objective */
  RecoveryTimeObjective: string;
  /** Recovery Point Objective */
  RecoveryPointObjective: string;
  /** Resource type */
  Type: string;
  /** Business impact level */
  BusinessImpact: string;
  /** Compliance requirements */
  ComplianceRequirement: string;
  /** Observability enabled */
  Observability: string;
  /** Environment name */
  Environment: string;
}

/**
 * Main configuration interface
 * This represents the complete structure of environment configuration files
 */
export interface AppConfig {
  /** AWS account ID */
  account: string;
  /** AWS region */
  region: string;
  /** Environment identifier */
  environment: string;
  /** Stack name */
  stackName: string;
  /** Resource tags */
  tags: ResourceTags;
  /** VPC configuration */
  vpc: VpcConfig;
  /** Secrets Manager configuration */
  secretsManager: SecretsManagerConfig;
  /** Lambda configuration */
  lambda: LambdaConfig;
  /** Default Stack Synthesizer configuration */
  defaultStackSynthesizer: DefaultStackSynthesizerConfig;
  /** Stacks configuration */
  stacks: StacksConfig;
}

/**
 * Type guard to check if an object is a valid AppConfig
 */
export function isAppConfig(obj: any): obj is AppConfig {
  return (
    typeof obj === 'object' &&
    typeof obj.account === 'string' &&
    typeof obj.region === 'string' &&
    typeof obj.environment === 'string' &&
    typeof obj.stackName === 'string' &&
    typeof obj.tags === 'object' &&
    typeof obj.vpc === 'object' &&
    typeof obj.secretsManager === 'object' &&
    typeof obj.lambda === 'object' &&
    typeof obj.defaultStackSynthesizer === 'object' &&
    typeof obj.stacks === 'object'
  );
}

/**
 * Valid environment names
 */
export type ValidEnvironment = 'int-cac1' | 'bat-cac1' | 'crt-cac1' | 'preprod-cac1' | 'prod-cac1';

/**
 * Environment validation function
 */
export function isValidEnvironment(env: string): env is ValidEnvironment {
  return ['int-cac1', 'bat-cac1', 'crt-cac1', 'preprod-cac1', 'prod-cac1'].includes(env);
}
