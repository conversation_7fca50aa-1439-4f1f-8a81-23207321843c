#!/usr/bin/env ts-node

/**
 * Configuration validation script
 * This script validates all configuration files against the defined interface
 */

import * as fs from 'fs';
import * as path from 'path';
import { AppConfig, isAppConfig, ValidEnvironment } from './config.interface';
import { getValidEnvironments } from '../utils/config-util';

/**
 * Validates a single configuration file
 * @param filePath - Path to the configuration file
 * @returns Validation result
 */
function validateConfigFile(filePath: string): { valid: boolean; errors: string[] } {
  const errors: string[] = [];
  
  try {
    // Check if file exists
    if (!fs.existsSync(filePath)) {
      errors.push(`Configuration file not found: ${filePath}`);
      return { valid: false, errors };
    }

    // Read and parse JSON
    const fileContent = fs.readFileSync(filePath, 'utf8');
    let config: any;
    
    try {
      config = JSON.parse(fileContent);
    } catch (parseError) {
      errors.push(`Invalid JSON in ${filePath}: ${parseError}`);
      return { valid: false, errors };
    }

    // Validate against interface
    if (!isAppConfig(config)) {
      errors.push(`Configuration structure validation failed for ${filePath}`);
      
      // Detailed validation
      const requiredFields = [
        'account', 'region', 'environment', 'stackName', 'tags', 
        'vpc', 'secretsManager', 'lambda', 'defaultStackSynthesizer', 'stacks'
      ];
      
      for (const field of requiredFields) {
        if (!(field in config)) {
          errors.push(`Missing required field: ${field}`);
        }
      }
      
      return { valid: false, errors };
    }

    // Additional validations
    if (config.account && !/^\d{12}$/.test(config.account)) {
      errors.push(`Invalid AWS account ID format: ${config.account}`);
    }

    if (config.region && !config.region.match(/^[a-z]{2}-[a-z]+-\d+$/)) {
      errors.push(`Invalid AWS region format: ${config.region}`);
    }

    if (config.lambda?.timeout && (config.lambda.timeout < 1 || config.lambda.timeout > 900)) {
      errors.push(`Lambda timeout must be between 1 and 900 seconds: ${config.lambda.timeout}`);
    }

    if (config.lambda?.memorySize && (config.lambda.memorySize < 128 || config.lambda.memorySize > 10240)) {
      errors.push(`Lambda memory size must be between 128 and 10240 MB: ${config.lambda.memorySize}`);
    }

    return { valid: errors.length === 0, errors };
    
  } catch (error) {
    errors.push(`Unexpected error validating ${filePath}: ${error}`);
    return { valid: false, errors };
  }
}

/**
 * Validates all configuration files
 */
function validateAllConfigs(): void {
  console.log('🔍 Validating configuration files...\n');
  
  const configDir = __dirname;
  const environments = getValidEnvironments();
  let allValid = true;
  
  for (const env of environments) {
    const configFile = path.join(configDir, `${env}.json`);
    console.log(`Validating ${env}.json...`);
    
    const result = validateConfigFile(configFile);
    
    if (result.valid) {
      console.log(`✅ ${env}.json is valid`);
    } else {
      console.log(`❌ ${env}.json has errors:`);
      result.errors.forEach(error => console.log(`   - ${error}`));
      allValid = false;
    }
    console.log();
  }
  
  if (allValid) {
    console.log('🎉 All configuration files are valid!');
    process.exit(0);
  } else {
    console.log('💥 Some configuration files have errors. Please fix them before proceeding.');
    process.exit(1);
  }
}

/**
 * Main execution
 */
if (require.main === module) {
  // Check if a specific file was provided as argument
  const args = process.argv.slice(2);
  
  if (args.length > 0) {
    const configFile = args[0];
    console.log(`🔍 Validating ${configFile}...\n`);
    
    const result = validateConfigFile(configFile);
    
    if (result.valid) {
      console.log(`✅ ${configFile} is valid`);
      process.exit(0);
    } else {
      console.log(`❌ ${configFile} has errors:`);
      result.errors.forEach(error => console.log(`   - ${error}`));
      process.exit(1);
    }
  } else {
    validateAllConfigs();
  }
}

export { validateConfigFile, validateAllConfigs };
